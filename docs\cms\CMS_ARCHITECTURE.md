# CMS 架构设计文档

## 概述

本文档详细描述了 ShipAny 项目的 CMS（内容管理系统）架构设计。我们采用了分层架构模式，通过抽象层实现了对不同 CMS 提供者的统一接口，确保了系统的可扩展性和可维护性。

## 架构原则

### 1. 提供者无关性
- 通过抽象层隔离具体的 CMS 实现
- 支持多种 CMS 后端（Contentlayer2、Strapi、Sanity、Contentful 等）
- 统一的 API 接口，便于切换不同的 CMS 提供者

### 2. 类型安全
- 全面的 TypeScript 类型定义
- 编译时类型检查
- 自动类型推导和智能提示

### 3. 性能优化
- 多层缓存策略（内存、Redis、文件）
- 智能缓存失效机制
- 按需加载和预取策略

### 4. SEO 友好
- 自动生成结构化数据
- 多语言 sitemap 支持
- RSS 订阅源生成
- Open Graph 和 Twitter Cards 支持

## 目录结构

```
src/cms/
├── types/                    # 类型定义
│   ├── index.ts             # 核心类型和接口
│   ├── seo.ts               # SEO 相关类型
│   └── cache.ts             # 缓存相关类型
├── providers/               # CMS 提供者实现
│   ├── contentlayer2/       # Contentlayer2 提供者
│   ├── strapi/              # Strapi 提供者（占位）
│   ├── sanity/              # Sanity 提供者（占位）
│   ├── contentful/          # Contentful 提供者（占位）
│   └── nextjs-mdx/          # Next.js MDX 提供者（占位）
└── services/                # 服务层
    ├── index.ts             # 主要 CMS 服务
    ├── seo.ts               # SEO 服务
    └── cache/               # 缓存服务
        ├── index.ts         # 缓存服务主入口
        └── memory-cache.ts  # 内存缓存实现
```

## 核心组件

### 1. 类型系统 (`src/cms/types/`)

#### 核心接口

```typescript
// 内容提供者接口
interface ContentProvider {
  readonly name: string
  readonly version: string
  
  getContent<T extends ContentItem>(type: ContentType, slug: string, locale: string): Promise<T | null>
  getContentList<T extends ContentItem>(type: ContentType, locale: string, options?: QueryOptions): Promise<T[]>
  contentExists(type: ContentType, slug: string, locale: string): Promise<boolean>
  getContentTitle(type: ContentType, slug: string, locale: string): Promise<string | null>
  getContentMetadata(type: ContentType, slug: string, locale: string): Promise<ContentMetadata | null>
  getAvailableLanguages(type: ContentType, slug: string): Promise<LanguageVersion[]>
  getRelatedContent?<T extends ContentItem>(type: ContentType, currentSlug: string, locale: string, limit?: number): Promise<T[]>
}

// 内容项基础接口
interface BaseContentItem {
  slug: string
  title: string
  lang: string
  url: string
  description?: string
  body: { raw: string; html: string }
  coverImage?: string
  author?: string
  publishedAt?: string
  createdAt: string
  featured: boolean
  tags?: string[]
}
```

#### SEO 类型

```typescript
// 结构化数据类型
interface BlogPostingStructuredData extends BaseStructuredData {
  '@type': 'BlogPosting'
  headline: string
  author: PersonObject | OrganizationObject
  publisher: OrganizationObject
  mainEntityOfPage: { '@type': 'WebPage'; '@id': string }
  keywords?: string
  wordCount?: number
  timeRequired?: string
}

// 元标签类型
interface MetaTags extends BasicMetaTags {
  openGraph: OpenGraphTags
  twitter: TwitterCardTags
  structuredData?: StructuredData
}
```

#### 缓存类型

```typescript
// 缓存提供者接口
interface CacheProvider {
  readonly name: string
  readonly strategy: CacheStrategy
  
  get<T>(key: string): Promise<T | null>
  set<T>(key: string, value: T, ttl?: number): Promise<void>
  delete(key: string): Promise<boolean>
  exists(key: string): Promise<boolean>
  
  // 批量操作
  getMany<T>(keys: string[]): Promise<(T | null)[]>
  setMany<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<void>
  deleteMany(keys: string[]): Promise<number>
  
  // 模式匹配
  deleteByPattern(pattern: string): Promise<number>
  getKeysByPattern(pattern: string): Promise<string[]>
  
  // 标签操作
  getByTag<T>(tag: string): Promise<T[]>
  deleteByTag(tag: string): Promise<number>
  tagKey(key: string, tags: string[]): Promise<void>
}
```

### 2. 提供者层 (`src/cms/providers/`)

#### 当前实现状态

| 提供者 | 状态 | 描述 |
|--------|------|------|
| Contentlayer2 | ✅ 已实现 | 基于文件系统的 MDX 处理，支持 Next.js 15 |
| Next.js MDX | 🚧 占位实现 | 原生 Next.js MDX 支持，作为备选方案 |
| Strapi | 🚧 占位实现 | 开源 Headless CMS |
| Sanity | 🚧 占位实现 | 实时协作 CMS，支持 GROQ 查询 |
| Contentful | 🚧 占位实现 | 企业级 Headless CMS |

#### Contentlayer2 提供者

```typescript
export class Contentlayer2Provider implements ContentProvider {
  readonly name = 'contentlayer2'
  readonly version = '0.4.6'

  // 从 contentlayer2/generated 导入生成的内容
  private getCollection(type: ContentType): ContentItem[] {
    const collections = {
      blog: allBlogs,
      product: allProducts,
      'case-study': allCaseStudies
    }
    return collections[type] || []
  }

  async getContent<T extends ContentItem>(type: ContentType, slug: string, locale: string): Promise<T | null> {
    const collection = this.getCollection(type)
    const content = collection.find(item => item.slug === slug && item.lang === locale)
    return (content as T) || null
  }
  
  // ... 其他方法实现
}
```

### 3. 服务层 (`src/cms/services/`)

#### CMS 主服务

```typescript
export class CMSService {
  private provider: ContentProvider | null = null
  private config: CMSConfig | null = null

  async initialize(config: CMSConfig): Promise<void> {
    this.config = config
    
    switch (config.provider) {
      case 'contentlayer2':
        this.provider = new Contentlayer2Provider()
        break
      // ... 其他提供者
    }
  }

  async getContent<T extends ContentItem>(type: ContentType, slug: string, locale: string): Promise<T | null> {
    this.ensureInitialized()
    return this.provider!.getContent<T>(type, slug, locale)
  }
  
  // ... 其他方法
}
```

#### SEO 服务

```typescript
export class SEOServiceImpl implements SEOService {
  async generateMetaTags(content: ContentItem, locale: string): Promise<MetaTags> {
    // 生成完整的元标签
    return {
      title: content.title,
      description: content.description || `${content.title} - ${this.siteName}`,
      openGraph: { /* Open Graph 标签 */ },
      twitter: { /* Twitter Cards 标签 */ },
      structuredData: await this.generateStructuredData(content)
    }
  }

  async generateStructuredData(content: ContentItem): Promise<StructuredData> {
    // 根据内容类型生成相应的结构化数据
    const contentType = this.getContentTypeFromUrl(content.url)
    
    switch (contentType) {
      case 'blog':
        return { '@type': 'BlogPosting', /* ... */ }
      case 'product':
        return { '@type': 'SoftwareApplication', /* ... */ }
      default:
        return { '@type': 'Article', /* ... */ }
    }
  }
  
  // ... 其他 SEO 方法
}
```

#### 缓存服务

```typescript
export class CacheServiceImpl implements CacheService {
  private provider: CacheProvider
  private keyBuilder: CacheKeyBuilder

  async cacheContent<T extends ContentItem>(type: ContentType, slug: string, locale: string, content: T): Promise<void> {
    const key = this.keyBuilder.contentKey(type, slug, locale)
    const ttl = this.config.ttl.content
    
    await this.provider.set(key, content, ttl)
    
    // 添加标签用于批量失效
    const tags = [`content:${type}`, `locale:${locale}`, `content:${type}:${locale}`]
    await this.provider.tagKey(key, tags)
  }
  
  // ... 其他缓存方法
}
```

## 使用方式

### 1. 初始化 CMS

```typescript
import { initializeCMS } from '@/cms/services'

// 使用默认配置初始化
await initializeCMS()

// 或使用自定义配置
await initializeCMS({
  provider: 'contentlayer2',
  contentTypes: ['blog', 'product', 'case-study'],
  defaultLocale: 'en',
  supportedLocales: ['en', 'zh'],
  features: {
    cache: true,
    seo: true,
    relatedContent: true,
    languageSwitching: true
  }
})
```

### 2. 获取内容

```typescript
import { cms } from '@/cms/services'

// 获取单个内容项
const blog = await cms.getContent('blog', 'my-post', 'en')

// 获取内容列表
const products = await cms.getContentList('product', 'zh', {
  sortBy: 'publishedAt',
  order: 'desc',
  featured: true,
  limit: 10
})

// 检查内容是否存在
const exists = await cms.contentExists('case-study', 'success-story', 'en')

// 获取相关内容
const related = await cms.getRelatedContent('blog', 'current-post', 'en', 3)
```

### 3. SEO 数据生成

```typescript
import { seoService } from '@/cms/services/seo'

// 生成元标签
const metaTags = await seoService.generateMetaTags(content, 'en')

// 生成结构化数据
const structuredData = await seoService.generateStructuredData(content)

// 生成 sitemap
const sitemap = await seoService.generateSitemap(allContent, sitemapConfig)

// 生成 RSS 订阅源
const rss = await seoService.generateRSSFeed(blogContent, rssConfig, 'en')
```

### 4. 缓存操作

```typescript
import { cacheService } from '@/cms/services/cache'

// 缓存内容
await cacheService.cacheContent('blog', 'my-post', 'en', blogContent)

// 获取缓存内容
const cached = await cacheService.getCachedContent('blog', 'my-post', 'en')

// 失效缓存
await cacheService.invalidateContent('blog', 'my-post', 'en')

// 获取缓存统计
const stats = await cacheService.getStats()
```

## 扩展指南

### 添加新的 CMS 提供者

1. **创建提供者类**
   ```typescript
   // src/cms/providers/new-cms/provider.ts
   export class NewCMSProvider implements ContentProvider {
     readonly name = 'new-cms'
     readonly version = '1.0.0'
     
     // 实现所有必需的方法
   }
   ```

2. **注册提供者**
   ```typescript
   // src/cms/services/index.ts
   switch (config.provider) {
     case 'new-cms':
       this.provider = new NewCMSProvider(config.newCmsConfig)
       break
   }
   ```

3. **更新类型定义**
   ```typescript
   // src/cms/types/index.ts
   export interface CMSConfig {
     provider: 'contentlayer2' | 'strapi' | 'sanity' | 'contentful' | 'new-cms'
     // ...
   }
   ```

### 添加新的缓存策略

1. **实现缓存提供者**
   ```typescript
   // src/cms/services/cache/redis-cache.ts
   export class RedisCacheProvider implements CacheProvider {
     readonly name = 'redis'
     readonly strategy: CacheStrategy = 'redis'
     
     // 实现所有必需的方法
   }
   ```

2. **注册缓存提供者**
   ```typescript
   // src/cms/services/cache/index.ts
   switch (config.strategy) {
     case 'redis':
       this.provider = new RedisCacheProvider(config.redis)
       break
   }
   ```

## 最佳实践

### 1. 错误处理
- 所有异步操作都应该有适当的错误处理
- 使用自定义错误类型提供更好的错误信息
- 实现优雅降级机制

### 2. 性能优化
- 合理使用缓存，避免过度缓存
- 实现智能预取策略
- 监控缓存命中率和性能指标

### 3. 类型安全
- 充分利用 TypeScript 的类型系统
- 避免使用 `any` 类型
- 为所有公共 API 提供完整的类型定义

### 4. 测试策略
- 为每个提供者编写单元测试
- 实现集成测试验证不同组件的协作
- 使用模拟数据进行测试

## 迁移指南

### 从 Contentlayer 到 Contentlayer2

1. **更新依赖**
   ```bash
   npm uninstall contentlayer next-contentlayer
   npm install contentlayer2 next-contentlayer2
   ```

2. **更新导入**
   ```typescript
   // 旧的导入
   import { allBlogs } from 'contentlayer/generated'
   
   // 新的导入
   import { allBlogs } from 'contentlayer2/generated'
   ```

3. **更新配置**
   ```typescript
   // contentlayer.config.ts
   import { defineDocumentType, makeSource } from 'contentlayer2/source-files'
   ```

### 迁移到 Headless CMS

1. **准备数据迁移脚本**
2. **配置新的 CMS 提供者**
3. **更新 CMS 配置**
4. **测试内容获取和显示**
5. **部署和监控**

## 总结

这个 CMS 架构设计提供了：

- **灵活性**：支持多种 CMS 后端
- **可扩展性**：易于添加新的提供者和功能
- **性能**：多层缓存和优化策略
- **SEO 友好**：完整的 SEO 支持
- **类型安全**：全面的 TypeScript 支持
- **可维护性**：清晰的分层架构和文档

通过这个架构，我们可以根据项目需求灵活选择最适合的 CMS 解决方案，同时保持代码的一致性和可维护性。
