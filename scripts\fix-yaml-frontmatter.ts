#!/usr/bin/env tsx

/**
 * Fix YAML Frontmatter Script
 * 
 * This script fixes YAML frontmatter formatting issues in MDX files
 * by converting quoted array syntax to proper YAML list syntax.
 */

import fs from 'fs'
import path from 'path'
import { glob } from 'glob'

async function fixYamlFrontmatter() {
  console.log('🔧 Fixing YAML frontmatter in MDX files...')
  
  // Find all MDX files in content directory
  const mdxFiles = await glob('content/**/*.mdx', { 
    cwd: process.cwd(),
    absolute: true 
  })
  
  let fixedCount = 0
  
  for (const filePath of mdxFiles) {
    try {
      const content = fs.readFileSync(filePath, 'utf-8')
      let updatedContent = content
      
      // Fix tags array format - remove all quotes from array items
      updatedContent = updatedContent.replace(
        /(\s+- )"([^"]+)"/g,
        '$1$2'
      )
      
      // Fix videoDuration quotes
      updatedContent = updatedContent.replace(
        /videoDuration:\s*"([^"]+)"/g,
        'videoDuration: $1'
      )
      
      // Fix icon quotes
      updatedContent = updatedContent.replace(
        /icon:\s*"([^"]+)"/g,
        'icon: $1'
      )
      
      if (updatedContent !== content) {
        fs.writeFileSync(filePath, updatedContent, 'utf-8')
        console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)}`)
        fixedCount++
      }
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error)
    }
  }
  
  console.log(`\n🎉 Fixed ${fixedCount} files`)
}

fixYamlFrontmatter().catch(console.error)
