# Docker 开发环境启动指南

## 🚀 快速启动

### 1. 启动容器
```bash
# 后台启动（推荐）
docker-compose up -d

# 前台启动（查看日志）
docker-compose up
```

### 2. 进入容器
```bash
docker-compose exec dev bash
```

### 3. 运行开发服务器
```bash
# Next.js 开发服务器
pnpm dev

# Cloudflare Workers 预览
pnpm cf:preview
```

### 4. 访问应用
- Next.js: http://localhost:3000
- Cloudflare Workers: http://localhost:8787

## 🛠️ 常用命令

### 容器管理
```bash
# 查看容器状态
docker-compose ps

# 查看容器日志
docker-compose logs dev

# 停止容器
docker-compose down

# 重新构建容器
docker-compose build --no-cache
```

### 开发命令（在容器内执行）
```bash
# 安装依赖
pnpm install

# 运行测试
pnpm test

# 构建项目
pnpm build

# Cloudflare 相关
wrangler whoami          # 验证认证
wrangler tail           # 查看日志
pnpm cf:deploy          # 部署到生产环境
```

## 🔧 故障排除

### 容器无法启动
1. 检查Docker是否运行
2. 检查端口3000和8787是否被占用
3. 重新构建容器：`docker-compose build --no-cache`

### 依赖安装失败
1. 进入容器：`docker-compose exec dev bash`
2. 清理缓存：`pnpm store prune`
3. 重新安装：`pnpm install`

### Cloudflare认证问题
1. 检查`.env.docker`中的API Token和Account ID
2. 在容器内运行：`wrangler whoami`
3. 如需重新认证：`wrangler auth login`

## 📁 目录结构
```
项目根目录/
├── Dockerfile.dev          # 开发环境Dockerfile
├── docker-compose.yml      # Docker Compose配置
├── .env.docker             # Docker环境变量
└── docs/
    └── DOCKER_STARTUP_GUIDE.md  # 本文档
```

## 🎯 下一步
- 配置IDE的Docker集成
- 设置代码热重载
- 配置调试环境
