/**
 * Contentlayer2 Provider Tests
 * 
 * Comprehensive test suite for the Contentlayer2 provider implementation.
 * These tests verify that the provider correctly implements the ContentProvider
 * interface and handles various content operations as expected.
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { Contentlayer2Provider } from '../providers/contentlayer2/provider'
import type { ContentItem, QueryOptions } from '../types'

// Mock contentlayer2/generated module
jest.mock('contentlayer2/generated', () => ({
  allBlogs: [
    {
      slug: 'test-blog-en',
      title: 'Test Blog Post',
      lang: 'en',
      url: '/blogs/en/test-blog-en',
      description: 'A test blog post in English',
      body: {
        raw: 'This is a test blog post content.',
        html: '<p>This is a test blog post content.</p>'
      },
      coverImage: '/images/test-blog.jpg',
      author: 'Test Author',
      publishedAt: '2024-01-01',
      createdAt: '2024-01-01T00:00:00Z',
      featured: true,
      tags: ['test', 'blog']
    },
    {
      slug: 'test-blog-zh',
      title: '测试博客文章',
      lang: 'zh',
      url: '/blogs/zh/test-blog-zh',
      description: '一篇中文测试博客文章',
      body: {
        raw: '这是一篇测试博客文章内容。',
        html: '<p>这是一篇测试博客文章内容。</p>'
      },
      coverImage: '/images/test-blog-zh.jpg',
      author: '测试作者',
      publishedAt: '2024-01-02',
      createdAt: '2024-01-02T00:00:00Z',
      featured: false,
      tags: ['测试', '博客']
    },
    {
      slug: 'another-blog-en',
      title: 'Another Blog Post',
      lang: 'en',
      url: '/blogs/en/another-blog-en',
      description: 'Another test blog post',
      body: {
        raw: 'Another test content.',
        html: '<p>Another test content.</p>'
      },
      publishedAt: '2024-01-03',
      createdAt: '2024-01-03T00:00:00Z',
      featured: false,
      tags: ['test']
    }
  ],
  allProducts: [
    {
      slug: 'test-product',
      title: 'Test Product',
      lang: 'en',
      url: '/products/en/test-product',
      description: 'A test product',
      body: {
        raw: 'Test product description.',
        html: '<p>Test product description.</p>'
      },
      publishedAt: '2024-01-01',
      createdAt: '2024-01-01T00:00:00Z',
      featured: true,
      tags: ['product', 'test']
    }
  ],
  allCaseStudies: [
    {
      slug: 'test-case-study',
      title: 'Test Case Study',
      lang: 'en',
      url: '/case-studies/en/test-case-study',
      description: 'A test case study',
      body: {
        raw: 'Test case study content.',
        html: '<p>Test case study content.</p>'
      },
      publishedAt: '2024-01-01',
      createdAt: '2024-01-01T00:00:00Z',
      featured: false,
      tags: ['case-study', 'test']
    }
  ]
}))

describe('Contentlayer2Provider', () => {
  let provider: Contentlayer2Provider

  beforeEach(() => {
    provider = new Contentlayer2Provider()
  })

  describe('Provider Information', () => {
    it('should have correct name and version', () => {
      expect(provider.name).toBe('contentlayer2')
      expect(provider.version).toBe('0.4.6')
    })
  })

  describe('getContent', () => {
    it('should return content item when found', async () => {
      const content = await provider.getContent('blog', 'test-blog-en', 'en')
      
      expect(content).toBeDefined()
      expect(content?.slug).toBe('test-blog-en')
      expect(content?.title).toBe('Test Blog Post')
      expect(content?.lang).toBe('en')
      expect(content?.featured).toBe(true)
    })

    it('should return null when content not found', async () => {
      const content = await provider.getContent('blog', 'non-existent', 'en')
      expect(content).toBeNull()
    })

    it('should return null when locale not found', async () => {
      const content = await provider.getContent('blog', 'test-blog-en', 'fr')
      expect(content).toBeNull()
    })

    it('should handle different content types', async () => {
      const product = await provider.getContent('product', 'test-product', 'en')
      const caseStudy = await provider.getContent('case-study', 'test-case-study', 'en')
      
      expect(product?.title).toBe('Test Product')
      expect(caseStudy?.title).toBe('Test Case Study')
    })
  })

  describe('getContentList', () => {
    it('should return all content for a type and locale', async () => {
      const blogs = await provider.getContentList('blog', 'en')
      
      expect(blogs).toHaveLength(2)
      expect(blogs[0].lang).toBe('en')
      expect(blogs[1].lang).toBe('en')
    })

    it('should filter by locale correctly', async () => {
      const blogsEn = await provider.getContentList('blog', 'en')
      const blogsZh = await provider.getContentList('blog', 'zh')
      
      expect(blogsEn).toHaveLength(2)
      expect(blogsZh).toHaveLength(1)
      expect(blogsZh[0].lang).toBe('zh')
    })

    it('should filter by featured status', async () => {
      const featuredBlogs = await provider.getContentList('blog', 'en', { featured: true })
      const nonFeaturedBlogs = await provider.getContentList('blog', 'en', { featured: false })
      
      expect(featuredBlogs).toHaveLength(1)
      expect(featuredBlogs[0].featured).toBe(true)
      expect(nonFeaturedBlogs).toHaveLength(1)
      expect(nonFeaturedBlogs[0].featured).toBe(false)
    })

    it('should filter by tags', async () => {
      const testBlogs = await provider.getContentList('blog', 'en', { tags: ['test'] })
      
      expect(testBlogs).toHaveLength(2)
      testBlogs.forEach(blog => {
        expect(blog.tags).toContain('test')
      })
    })

    it('should apply limit', async () => {
      const limitedBlogs = await provider.getContentList('blog', 'en', { limit: 1 })
      
      expect(limitedBlogs).toHaveLength(1)
    })

    it('should sort by publishedAt descending', async () => {
      const sortedBlogs = await provider.getContentList('blog', 'en', {
        sortBy: 'publishedAt',
        order: 'desc'
      })
      
      expect(sortedBlogs).toHaveLength(2)
      expect(new Date(sortedBlogs[0].publishedAt!).getTime())
        .toBeGreaterThan(new Date(sortedBlogs[1].publishedAt!).getTime())
    })

    it('should return empty array for unknown content type', async () => {
      const unknownContent = await provider.getContentList('unknown' as any, 'en')
      expect(unknownContent).toHaveLength(0)
    })
  })

  describe('contentExists', () => {
    it('should return true for existing content', async () => {
      const exists = await provider.contentExists('blog', 'test-blog-en', 'en')
      expect(exists).toBe(true)
    })

    it('should return false for non-existing content', async () => {
      const exists = await provider.contentExists('blog', 'non-existent', 'en')
      expect(exists).toBe(false)
    })

    it('should return false for wrong locale', async () => {
      const exists = await provider.contentExists('blog', 'test-blog-en', 'fr')
      expect(exists).toBe(false)
    })
  })

  describe('getContentTitle', () => {
    it('should return title for existing content', async () => {
      const title = await provider.getContentTitle('blog', 'test-blog-en', 'en')
      expect(title).toBe('Test Blog Post')
    })

    it('should return null for non-existing content', async () => {
      const title = await provider.getContentTitle('blog', 'non-existent', 'en')
      expect(title).toBeNull()
    })
  })

  describe('getContentMetadata', () => {
    it('should return metadata for existing content', async () => {
      const metadata = await provider.getContentMetadata('blog', 'test-blog-en', 'en')
      
      expect(metadata).toBeDefined()
      expect(metadata?.wordCount).toBeGreaterThan(0)
      expect(metadata?.readingTime).toBeGreaterThan(0)
      expect(metadata?.author).toBe('Test Author')
      expect(metadata?.tags).toEqual(['test', 'blog'])
    })

    it('should return null for non-existing content', async () => {
      const metadata = await provider.getContentMetadata('blog', 'non-existent', 'en')
      expect(metadata).toBeNull()
    })
  })

  describe('getAvailableLanguages', () => {
    it('should return available language versions', async () => {
      // Mock data has both 'test-blog-en' and 'test-blog-zh' with same slug pattern
      const languages = await provider.getAvailableLanguages('blog', 'test-blog-en')
      
      expect(languages).toHaveLength(1)
      expect(languages[0].lang).toBe('en')
      expect(languages[0].available).toBe(true)
    })

    it('should return empty array for non-existing content', async () => {
      const languages = await provider.getAvailableLanguages('blog', 'non-existent')
      expect(languages).toHaveLength(0)
    })
  })

  describe('getRelatedContent', () => {
    it('should return related content based on tags', async () => {
      const related = await provider.getRelatedContent('blog', 'test-blog-en', 'en', 2)
      
      expect(related).toHaveLength(1) // Only one other blog with 'test' tag
      expect(related[0].slug).toBe('another-blog-en')
    })

    it('should not include current content in results', async () => {
      const related = await provider.getRelatedContent('blog', 'test-blog-en', 'en', 5)
      
      expect(related.every(item => item.slug !== 'test-blog-en')).toBe(true)
    })

    it('should respect limit parameter', async () => {
      const related = await provider.getRelatedContent('blog', 'test-blog-en', 'en', 1)
      
      expect(related).toHaveLength(1)
    })

    it('should return empty array when no related content found', async () => {
      const related = await provider.getRelatedContent('product', 'test-product', 'en', 3)
      
      expect(related).toHaveLength(0)
    })
  })

  describe('Error Handling', () => {
    it('should handle errors gracefully in getContent', async () => {
      // Mock console.error to avoid noise in test output
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
      
      // This should not throw but return null
      const content = await provider.getContent('blog', 'test', 'en')
      
      consoleSpy.mockRestore()
    })

    it('should handle errors gracefully in getContentList', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
      
      // This should not throw but return empty array
      const content = await provider.getContentList('blog', 'en')
      
      expect(Array.isArray(content)).toBe(true)
      
      consoleSpy.mockRestore()
    })
  })
})
