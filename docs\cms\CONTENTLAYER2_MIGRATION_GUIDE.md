# Contentlayer2 迁移实施指南

## 概述

本文档提供了从 Contentlayer 迁移到 Contentlayer2 的完整实施指南。Contentlayer2 是社区维护的 fork，支持 Next.js 15 和 React 19，解决了原版 Contentlayer 的兼容性问题。

## 迁移背景

### 为什么选择 Contentlayer2？

1. **兼容性问题解决**：原版 Contentlayer 不支持 Next.js 15/React 19
2. **社区维护**：活跃的社区维护，持续更新
3. **向后兼容**：API 完全兼容，迁移成本最低
4. **性能优化**：包含多项性能改进和 bug 修复

### 架构优势

- **分层架构**：通过 CMS 抽象层实现提供者无关性
- **类型安全**：完整的 TypeScript 支持
- **可扩展性**：支持多种 CMS 后端
- **性能优化**：多层缓存策略
- **SEO 友好**：完整的 SEO 支持

## 实施步骤

### 第一步：依赖更新

#### 1.1 更新 package.json

```bash
# 卸载旧依赖
npm uninstall contentlayer next-contentlayer

# 安装新依赖
npm install contentlayer2@^0.4.6 next-contentlayer2@^0.4.6
```

#### 1.2 更新构建脚本

```json
{
  "scripts": {
    "build": "contentlayer2 build && pnpm generate:content && next build"
  }
}
```

### 第二步：配置文件更新

#### 2.1 更新 next.config.mjs

```javascript
import { withContentlayer } from "next-contentlayer2";

/** @type {import('next').NextConfig} */
const nextConfig = {
  // ... 其他配置
};

export default withContentlayer(nextConfig);
```

#### 2.2 更新 contentlayer.config.ts

```typescript
import { defineDocumentType, makeSource } from 'contentlayer2/source-files'
import fs from 'fs'
import path from 'path'

// ... 其余配置保持不变
```

### 第三步：代码更新

#### 3.1 更新导入语句

将所有 `contentlayer/generated` 导入更新为 `contentlayer2/generated`：

```typescript
// 旧的导入
import { allBlogs, allProducts, allCaseStudies } from 'contentlayer/generated'

// 新的导入
import { allBlogs, allProducts, allCaseStudies } from 'contentlayer2/generated'
```

#### 3.2 受影响的文件列表

- `src/services/content/content-queries.ts`
- `src/app/[locale]/(default)/blogs/page.tsx`
- `src/app/[locale]/(default)/blogs/[slug]/page.tsx`
- `src/app/[locale]/(default)/products/page.tsx`
- `src/app/[locale]/(default)/products/[slug]/page.tsx`
- `src/app/[locale]/(default)/case-studies/page.tsx`
- `src/app/[locale]/(default)/case-studies/[slug]/page.tsx`

### 第四步：CMS 抽象层集成

#### 4.1 初始化 CMS 服务

```typescript
import { initializeCMS } from '@/cms/services'

// 在应用启动时初始化
await initializeCMS({
  provider: 'contentlayer2',
  contentTypes: ['blog', 'product', 'case-study'],
  defaultLocale: 'en',
  supportedLocales: ['en', 'zh'],
  features: {
    cache: true,
    seo: true,
    relatedContent: true,
    languageSwitching: true
  }
})
```

#### 4.2 使用统一 API

```typescript
import { cms } from '@/cms/services'

// 获取内容
const blog = await cms.getContent('blog', 'my-post', 'en')
const products = await cms.getContentList('product', 'zh', {
  sortBy: 'publishedAt',
  order: 'desc',
  featured: true
})
```

### 第五步：SEO 增强

#### 5.1 使用 SEO 服务

```typescript
import { seoService } from '@/cms/services/seo'

// 生成元标签
const metaTags = await seoService.generateMetaTags(content, 'en')

// 生成结构化数据
const structuredData = await seoService.generateStructuredData(content)
```

#### 5.2 更新 sitemap 和 RSS 生成

```typescript
// 生成 sitemap
const sitemap = await seoService.generateSitemap(allContent, {
  baseUrl: 'https://shipany.ai',
  defaultChangefreq: 'weekly',
  defaultPriority: 0.7,
  includeAlternates: true,
  staticPages: [
    { path: '/', priority: 1.0 },
    { path: '/about', priority: 0.8 }
  ]
})

// 生成 RSS
const rss = await seoService.generateRSSFeed(blogContent, {
  title: 'ShipAny Blog',
  description: 'Latest updates from ShipAny',
  baseUrl: 'https://shipany.ai',
  language: 'en',
  maxItems: 20,
  includeContent: true
}, 'en')
```

### 第六步：缓存配置

#### 6.1 配置缓存服务

```typescript
import { cacheService } from '@/cms/services/cache'

// 缓存内容
await cacheService.cacheContent('blog', 'my-post', 'en', blogContent)

// 获取缓存
const cached = await cacheService.getCachedContent('blog', 'my-post', 'en')

// 失效缓存
await cacheService.invalidateContent('blog', 'my-post', 'en')
```

## 测试和验证

### 运行迁移测试

```bash
# 运行 CMS 迁移测试脚本
pnpm tsx scripts/test-cms-migration.ts

# 运行单元测试
pnpm test src/cms/__tests__/

# 构建测试
pnpm build

# 启动开发服务器测试
pnpm dev
```

### 验证清单

- [ ] 所有内容页面正常加载
- [ ] 多语言切换功能正常
- [ ] SEO 元标签正确生成
- [ ] Sitemap 和 RSS 正常生成
- [ ] 缓存功能正常工作
- [ ] 构建过程无错误
- [ ] 类型检查通过

## 性能优化建议

### 1. 缓存策略

```typescript
// 配置缓存 TTL
const cacheConfig = {
  ttl: {
    content: 3600,      // 1 小时
    contentList: 1800,  // 30 分钟
    metadata: 7200,     // 2 小时
    seo: 3600,          // 1 小时
    sitemap: 86400,     // 24 小时
    rss: 3600           // 1 小时
  }
}
```

### 2. 构建优化

```bash
# 使用并行构建
CONTENTLAYER_PARALLEL=true pnpm build

# 启用增量构建
CONTENTLAYER_INCREMENTAL=true pnpm build
```

### 3. 开发体验优化

```typescript
// 开发环境配置
if (process.env.NODE_ENV === 'development') {
  // 启用热重载
  // 减少缓存 TTL
  // 启用详细日志
}
```

## 故障排除

### 常见问题

#### 1. 构建失败

```bash
# 清理缓存
rm -rf .contentlayer2
rm -rf .next

# 重新安装依赖
rm -rf node_modules pnpm-lock.yaml
pnpm install

# 重新构建
pnpm contentlayer2 build
pnpm build
```

#### 2. 类型错误

```bash
# 重新生成类型
pnpm contentlayer2 build

# 检查 TypeScript 配置
npx tsc --noEmit
```

#### 3. 导入错误

确保所有导入都已更新：

```bash
# 搜索旧的导入
grep -r "contentlayer/generated" src/
grep -r "next-contentlayer" .

# 替换为新的导入
sed -i 's/contentlayer\/generated/contentlayer2\/generated/g' src/**/*.ts
sed -i 's/next-contentlayer/next-contentlayer2/g' *.js *.ts
```

## 未来扩展计划

### 1. 备选方案准备

如果 Contentlayer2 出现问题，可以快速切换到：

- **Next.js MDX**：原生 MDX 支持
- **Strapi**：开源 Headless CMS
- **Sanity**：实时协作 CMS
- **Contentful**：企业级 CMS

### 2. 架构扩展

```typescript
// 添加新的 CMS 提供者
await initializeCMS({
  provider: 'strapi', // 或 'sanity', 'contentful'
  // ... 其他配置
})
```

### 3. 功能增强

- 实时内容更新
- 内容版本控制
- 高级缓存策略
- 内容分析和监控

## 部署注意事项

### 1. 环境变量

```bash
# .env.local
NEXT_PUBLIC_SITE_URL=https://shipany.ai
CONTENTLAYER_CACHE_DIR=.contentlayer2
```

### 2. 构建脚本

```json
{
  "scripts": {
    "build:content": "contentlayer2 build",
    "build:seo": "pnpm generate:content",
    "build:app": "next build",
    "build": "pnpm build:content && pnpm build:seo && pnpm build:app"
  }
}
```

### 3. CI/CD 配置

```yaml
# .github/workflows/deploy.yml
- name: Build content
  run: pnpm contentlayer2 build

- name: Generate SEO files
  run: pnpm generate:content

- name: Build application
  run: pnpm build
```

## 总结

Contentlayer2 迁移提供了：

1. **即时兼容性**：解决 Next.js 15/React 19 兼容问题
2. **零停机迁移**：API 完全兼容，无需重写代码
3. **架构升级**：引入 CMS 抽象层，提升可扩展性
4. **性能提升**：多层缓存和 SEO 优化
5. **未来保障**：为后续 CMS 迁移做好准备

通过这次迁移，我们不仅解决了当前的兼容性问题，还为未来的技术演进奠定了坚实的基础。
