/**
 * Cache Types Module
 * 
 * Comprehensive type definitions for caching functionality in the CMS system.
 * These types support multiple caching strategies including memory, Redis,
 * and file-based caching with TTL support and cache invalidation patterns.
 */

import type { ContentItem, ContentType } from './index'

/**
 * Cache Strategy Types
 * 
 * Defines the available caching strategies that can be used
 * by the CMS system for storing and retrieving content data.
 */

export type CacheStrategy = 'memory' | 'redis' | 'file' | 'hybrid'

/**
 * Cache Entry Interface
 * 
 * Represents a single cached item with metadata about when
 * it was cached, when it expires, and how to identify it.
 */

export interface CacheEntry<T = any> {
  // Cached data
  data: T
  
  // Metadata
  key: string
  timestamp: number
  ttl: number
  expiresAt: number
  
  // Optional metadata
  tags?: string[]
  version?: string
  size?: number
}

/**
 * Cache Key Builder Interface
 * 
 * Defines how cache keys are constructed for different
 * types of content and operations to ensure uniqueness
 * and enable efficient cache invalidation.
 */

export interface CacheKeyBuilder {
  // Content-specific keys
  contentKey(type: ContentType, slug: string, locale: string): string
  contentListKey(type: ContentType, locale: string, options?: any): string
  metadataKey(type: ContentType, slug: string, locale: string): string
  
  // SEO-specific keys
  seoKey(type: ContentType, slug: string, locale: string): string
  sitemapKey(locale?: string): string
  rssKey(locale: string): string
  
  // Utility methods
  parseKey(key: string): {
    type: string
    identifier: string
    locale?: string
  }
  
  generateTags(key: string): string[]
}

/**
 * Cache Configuration Interface
 * 
 * Defines configuration options for different caching
 * strategies including TTL settings, size limits,
 * and invalidation policies.
 */

export interface CacheConfig {
  // Strategy selection
  strategy: CacheStrategy
  
  // TTL settings (in seconds)
  ttl: {
    content: number
    contentList: number
    metadata: number
    seo: number
    sitemap: number
    rss: number
  }
  
  // Size limits
  maxSize?: number
  maxEntries?: number
  
  // Redis-specific settings
  redis?: {
    host: string
    port: number
    password?: string
    db?: number
    keyPrefix?: string
  }
  
  // File cache settings
  file?: {
    directory: string
    maxFileSize: number
    compression: boolean
  }
  
  // Memory cache settings
  memory?: {
    maxSize: number
    gcInterval: number
  }
  
  // Invalidation settings
  invalidation: {
    enabled: boolean
    patterns: string[]
    onContentChange: boolean
    onBuild: boolean
  }
}

/**
 * Cache Provider Interface
 * 
 * Defines the contract that all cache implementations
 * must follow to ensure consistent behavior across
 * different caching strategies.
 */

export interface CacheProvider {
  // Provider identification
  readonly name: string
  readonly strategy: CacheStrategy
  
  // Basic operations
  get<T>(key: string): Promise<T | null>
  set<T>(key: string, value: T, ttl?: number): Promise<void>
  delete(key: string): Promise<boolean>
  exists(key: string): Promise<boolean>
  
  // Batch operations
  getMany<T>(keys: string[]): Promise<(T | null)[]>
  setMany<T>(entries: Array<{ key: string; value: T; ttl?: number }>): Promise<void>
  deleteMany(keys: string[]): Promise<number>
  
  // Pattern-based operations
  deleteByPattern(pattern: string): Promise<number>
  getKeysByPattern(pattern: string): Promise<string[]>
  
  // Tag-based operations
  getByTag<T>(tag: string): Promise<T[]>
  deleteByTag(tag: string): Promise<number>
  tagKey(key: string, tags: string[]): Promise<void>
  
  // Cache management
  clear(): Promise<void>
  size(): Promise<number>
  stats(): Promise<CacheStats>
  
  // Lifecycle
  connect?(): Promise<void>
  disconnect?(): Promise<void>
}

/**
 * Cache Statistics Interface
 * 
 * Provides insights into cache performance and usage
 * for monitoring and optimization purposes.
 */

export interface CacheStats {
  // Hit/miss statistics
  hits: number
  misses: number
  hitRate: number
  
  // Size and capacity
  size: number
  maxSize?: number
  entryCount: number
  maxEntries?: number
  
  // Memory usage (in bytes)
  memoryUsage?: number
  
  // Performance metrics
  averageGetTime: number
  averageSetTime: number
  
  // Expiration statistics
  expiredEntries: number
  evictedEntries: number
  
  // Last reset time
  resetAt: number
}

/**
 * Cache Event Types
 * 
 * Defines events that can be emitted by the cache system
 * for monitoring, debugging, and integration purposes.
 */

export type CacheEventType = 
  | 'hit'
  | 'miss'
  | 'set'
  | 'delete'
  | 'expire'
  | 'evict'
  | 'clear'
  | 'error'

export interface CacheEvent {
  type: CacheEventType
  key: string
  timestamp: number
  metadata?: {
    ttl?: number
    size?: number
    tags?: string[]
    error?: Error
  }
}

/**
 * Cache Invalidation Interface
 * 
 * Defines how cache invalidation is handled when content
 * changes or when specific patterns need to be cleared.
 */

export interface CacheInvalidator {
  // Content-based invalidation
  invalidateContent(type: ContentType, slug: string, locale?: string): Promise<void>
  invalidateContentType(type: ContentType, locale?: string): Promise<void>
  invalidateLocale(locale: string): Promise<void>
  
  // Pattern-based invalidation
  invalidatePattern(pattern: string): Promise<void>
  invalidateTag(tag: string): Promise<void>
  
  // Bulk invalidation
  invalidateAll(): Promise<void>
  invalidateSEO(): Promise<void>
  
  // Conditional invalidation
  invalidateIf(condition: (key: string, entry: CacheEntry) => boolean): Promise<void>
}

/**
 * Cache Middleware Interface
 * 
 * Defines middleware that can be applied to cache operations
 * for logging, metrics collection, or custom processing.
 */

export interface CacheMiddleware {
  name: string
  
  // Operation hooks
  beforeGet?(key: string): Promise<void>
  afterGet?<T>(key: string, value: T | null, fromCache: boolean): Promise<void>
  
  beforeSet?<T>(key: string, value: T, ttl?: number): Promise<void>
  afterSet?<T>(key: string, value: T, ttl?: number): Promise<void>
  
  beforeDelete?(key: string): Promise<void>
  afterDelete?(key: string, deleted: boolean): Promise<void>
  
  // Error handling
  onError?(error: Error, operation: string, key: string): Promise<void>
}

/**
 * Cache Service Interface
 * 
 * High-level interface for cache operations that combines
 * multiple cache providers and provides content-specific
 * caching functionality.
 */

export interface CacheService {
  // Content caching
  cacheContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string,
    content: T
  ): Promise<void>
  
  getCachedContent<T extends ContentItem>(
    type: ContentType,
    slug: string,
    locale: string
  ): Promise<T | null>
  
  cacheContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    content: T[],
    options?: any
  ): Promise<void>
  
  getCachedContentList<T extends ContentItem>(
    type: ContentType,
    locale: string,
    options?: any
  ): Promise<T[] | null>
  
  // SEO caching
  cacheSEOData(key: string, data: any): Promise<void>
  getCachedSEOData<T>(key: string): Promise<T | null>
  
  // Cache management
  invalidateContent(type: ContentType, slug?: string, locale?: string): Promise<void>
  invalidateAll(): Promise<void>
  getStats(): Promise<CacheStats>
  
  // Middleware support
  use(middleware: CacheMiddleware): void
}

/**
 * Cache Utility Types
 * 
 * Helper types for working with cached content and
 * cache operations in a type-safe manner.
 */

export type CachedContent<T extends ContentType> = T extends 'blog'
  ? ContentItem
  : T extends 'product'
  ? ContentItem
  : ContentItem

export interface CacheWarmupConfig {
  contentTypes: ContentType[]
  locales: string[]
  preloadFeatured: boolean
  preloadRecent: number
  parallel: boolean
  batchSize: number
}

export interface CacheMetrics {
  provider: string
  strategy: CacheStrategy
  stats: CacheStats
  health: 'healthy' | 'degraded' | 'unhealthy'
  lastCheck: number
}
